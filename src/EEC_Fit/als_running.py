# 导入必要的库和模块
import numpy as np
from scipy.optimize import root_scalar
from typing import Dict, List, Tuple
from functools import lru_cache


class AlphaSRunner:
    """
    用于计算QCD中强耦合常数alpha_s的运行。

    该类实现了QCD强耦合常数alpha_s的运行，支持最高5圈的计算。
    使用重整化群方程(RGE)来计算不同能标下的alpha_s值，并在夸克质量阈值处
    进行匹配以保证连续性。

    Parameters
    ----------
    ALS_MZ : float, optional
        Z玻色子质量能标(M_Z)处的alpha_s值，默认为0.118
    max_loop : int, optional
        最大圈数，默认为5（对应5圈运行）

    Attributes
    ----------
    als_mz : float
        Z玻色子质量能标(M_Z)处的alpha_s值
    max_loop : int
        最大圈数
    lambdas : Dict[int, float]
        存储不同活跃夸克数nf对应的Lambda_QCD值的字典

    Raises
    ------
    ValueError
        当max_loop大于5时抛出异常

    Notes
    -----
    - 使用LRU缓存优化重复计算
    - 支持kappa参数进行能标缩放
    - 在夸克质量阈值处自动进行匹配

    Examples
    --------
    >>> runner = AlphaSRunner(ALS_MZ=0.118, max_loop=4)
    >>> alpha_s_100 = runner.alpha_s(100.0)  # 计算100 GeV处的alpha_s
    """

    # QCD色因子常数
    CA: float = 3.0  # SU(3)伴随表示的Casimir算子
    CF: float = 4.0 / 3.0  # SU(3)基本表示的Casimir算子
    TR: float = 0.5  # 归一化因子

    # 数学常数
    PI: float = np.pi

    # Riemann zeta函数值
    ZETA2: float = 1.64493  # ζ(2) = π²/6
    ZETA3: float = 1.20206  # ζ(3) (Apéry常数)
    ZETA4: float = 1.08232  # ζ(4) = π⁴/90
    ZETA5: float = 1.03693  # ζ(5)

    # 夸克质量阈值 (质量[GeV], 活跃夸克数nf)
    # 分别对应: charm, bottom, top夸克
    THRESH: List[Tuple[float, int]] = [(1.3, 4), (4.2, 5), (100000, 6)]

    # Z玻色子质量 [GeV]
    M_Z: float = 91.1876

    def __init__(self, ALS_MZ: float = 0.118, max_loop: int = 5):
        """
        初始化AlphaSRunner实例。

        Parameters
        ----------
        ALS_MZ : float, optional
            Z玻色子质量能标(M_Z)处的alpha_s值，默认为0.118
        max_loop : int, optional
            最大圈数，默认为5

        Raises
        ------
        ValueError
            当max_loop大于5时抛出异常
        """
        if max_loop > 5:
            raise ValueError("当前实现最多支持5圈运行")

        self.als_mz = ALS_MZ
        self.max_loop = max_loop
        self.lambdas: Dict[int, float] = {}
        self._setup_lambdas()

    def _setup_lambdas(self) -> None:
        """
        计算不同活跃夸克数nf对应的Lambda_QCD值。

        该方法首先在M_Z能标处计算nf=5时的Lambda值，然后通过在夸克质量阈值处
        进行匹配，依次计算nf=4,3时的Lambda值。

        Notes
        -----
        - 使用数值求根方法(Brent方法)求解Lambda值
        - 在夸克质量阈值处进行2圈匹配以保证连续性
        """
        print("--- 初始化 AlphaSRunner ---")

        # 首先计算nf=5时的Lambda值（在M_Z能标处）
        nf = 5

        def objective_lambda5(log_lambda_sq: float) -> float:
            """目标函数：使得在M_Z处计算的alpha_s等于输入值"""
            lambda_sq = np.exp(log_lambda_sq)
            als_val = self._als_from_lambda(self.M_Z**2, lambda_sq, nf)
            return als_val - self.als_mz

        sol = root_scalar(objective_lambda5, bracket=[-10, 10], method="brentq")
        lambda_5_sq = np.exp(sol.root)
        self.lambdas[5] = np.sqrt(lambda_5_sq)
        print(f"  -> 计算得到 Lambda(nf=5) = {self.lambdas[5]:.4f} GeV")

        # 通过阈值匹配计算其他nf值对应的Lambda
        for m_h, nf_high in reversed(self.THRESH):
            nf_low = nf_high - 1
            if nf_high not in self.lambdas:
                continue

            # 在阈值处计算alpha_s并进行匹配
            als_at_thresh = self._als_from_lambda(
                m_h**2, self.lambdas[nf_high] ** 2, nf_high
            )
            als_at_thresh_matched = self._match_als_at_threshold(als_at_thresh, nf_high)

            def objective_lambda_low(log_lambda_sq: float) -> float:
                """目标函数：使得在阈值处匹配后的alpha_s值相等"""
                lambda_sq = np.exp(log_lambda_sq)
                als_val = self._als_from_lambda(m_h**2, lambda_sq, nf_low)
                return als_val - als_at_thresh_matched

            sol = root_scalar(objective_lambda_low, bracket=[-10, 10], method="brentq")
            lambda_low_sq = np.exp(sol.root)
            self.lambdas[nf_low] = np.sqrt(lambda_low_sq)
            print(f"  -> 计算得到 Lambda(nf={nf_low}) = {self.lambdas[nf_low]:.4f} GeV")

        print("--- 初始化完成 ---")

    @lru_cache(maxsize=100000)
    def alpha_s(self, Q: float, kappa: float = 1.0) -> float:
        """
        计算给定能标Q处的强耦合常数alpha_s值。

        Parameters
        ----------
        Q : float
            重整化能标，单位为GeV
        kappa : float, optional
            能标缩放因子，默认为1.0。实际计算使用的能标为kappa*Q

        Returns
        -------
        float
            计算得到的alpha_s值

        Raises
        ------
        ValueError
            当能标超出有效范围时抛出异常

        Notes
        -----
        - 使用LRU缓存优化性能，避免重复计算
        - 自动根据能标选择合适的活跃夸克数nf
        - 当能标低于Lambda_QCD时返回无穷大
        """
        Q_scaled = kappa * Q
        nf = self._nf_at(Q_scaled)

        if nf not in self.lambdas:
            if Q_scaled < self.lambdas[3]:
                return float("inf")
            raise ValueError(
                f"未计算nf={nf}对应的Lambda值。能标Q={Q_scaled:.2f} GeV超出有效范围。"
            )

        lambda_sq = self.lambdas[nf] ** 2
        return self._als_from_lambda(Q_scaled**2, lambda_sq, nf)

    def _als_from_lambda(self, mu_sq: float, lambda_sq: float, nf: int) -> float:
        """
        根据Lambda_QCD值计算给定能标处的alpha_s。

        使用重整化群方程的解析解，支持最高5圈精度。

        Parameters
        ----------
        mu_sq : float
            重整化能标的平方，单位为GeV²
        lambda_sq : float
            Lambda_QCD的平方，单位为GeV²
        nf : int
            活跃夸克数

        Returns
        -------
        float
            计算得到的alpha_s值

        Notes
        -----
        - 当mu_sq <= lambda_sq时返回无穷大（非微扰区域）
        - 使用beta函数系数的展开式计算
        - 根据max_loop参数决定计算精度
        """
        if mu_sq <= lambda_sq:
            return float("inf")

        # 计算beta函数系数
        b0 = self._beta0(nf)
        b1 = self._beta1(nf)
        b2 = self._beta2(nf)
        b3 = self._beta3(nf)
        b4 = self._beta4(nf)

        # 定义对数变量
        t = np.log(mu_sq / lambda_sq)  # ln(μ²/Λ²)
        l = np.log(t)  # ln(ln(μ²/Λ²))

        # 各阶修正项
        s0 = 1.0  # 领头阶(LO)

        s1 = -(b1 * l) / (b0**2 * t)  # 次领头阶(NLO)

        s2 = (b1**2 * (l**2 - l - 1) + b0 * b2) / (b0**4 * t**2)  # NNLO

        s3 = (  # N³LO
            b1**3 * (-2 * l**3 + 5 * l**2 + 4 * l - 1)
            - 6 * b0 * b1 * b2 * l
            + b0**2 * b3
        ) / (2 * b0**6 * t**3)

        # N⁴LO修正项（分解为多个部分以提高可读性）
        term4_1 = 18 * b0 * b2 * b1**2 * (2 * l**2 - l - 1)
        term4_2 = b1**4 * (6 * l**4 - 26 * l**3 - 9 * l**2 + 24 * l + 7)
        term4_3 = -(b0**2) * b3 * b1 * (12 * l + 1)
        term4_4 = 2 * b0**2 * (5 * b2**2 + b0 * b4)
        s4 = (term4_1 + term4_2 + term4_3 + term4_4) / (6 * b0**8 * t**4)

        # 根据max_loop累加修正项
        total_sum = s0
        if self.max_loop >= 1:
            total_sum += s1
        if self.max_loop >= 2:
            total_sum += s2
        if self.max_loop >= 3:
            total_sum += s3
        if self.max_loop >= 4:
            total_sum += s4

        return (1 / (b0 * t)) * total_sum

    def _nf_at(self, q: float) -> int:
        """
        根据能标Q确定活跃夸克数nf。

        Parameters
        ----------
        q : float
            能标，单位为GeV

        Returns
        -------
        int
            活跃夸克数nf

        Notes
        -----
        - 当Q >= 100 TeV时，nf=6（包含top夸克）
        - 当Q >= 4.2 GeV时，nf=5（包含bottom夸克）
        - 当Q >= 1.3 GeV时，nf=4（包含charm夸克）
        - 当Q < 1.3 GeV时，nf=3（仅u,d,s夸克）
        """
        for m, nf in reversed(self.THRESH):
            if q >= m:
                return nf
        return 3

    def _match_als_at_threshold(self, als_nf_high: float, nf_high: int) -> float:
        """
        在夸克质量阈值处进行alpha_s匹配。

        当跨越夸克质量阈值时，活跃夸克数发生变化，需要对alpha_s进行
        匹配修正以保证物理量的连续性。

        Parameters
        ----------
        als_nf_high : float
            阈值上方（高nf）的alpha_s值
        nf_high : int
            阈值上方的活跃夸克数（当前未使用，保留用于未来扩展）

        Returns
        -------
        float
            阈值下方（低nf）匹配后的alpha_s值

        Notes
        -----
        - 当前实现使用2圈匹配公式
        - 匹配系数c20 = -11/(72π²)
        """
        # 2圈匹配修正
        c20 = -11.0 / (72.0 * self.PI**2)
        correction = -c20 * als_nf_high**2
        return als_nf_high * (1 + correction)

    def _beta0(self, nf: int) -> float:
        """
        计算beta函数的1圈系数β₀。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        float
            β₀系数值
        """
        return (33.0 - 2.0 * nf) / (12.0 * self.PI)

    def _beta1(self, nf: int) -> float:
        """
        计算beta函数的2圈系数β₁。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        float
            β₁系数值
        """
        return (153.0 - 19.0 * nf) / (24.0 * self.PI**2)

    def _beta2(self, nf: int) -> float:
        """
        计算beta函数的3圈系数β₂。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        float
            β₂系数值
        """
        return (77139.0 - 15099.0 * nf + 325.0 * nf**2) / (3456.0 * self.PI**3)

    def _beta3(self, nf: int) -> float:
        """
        计算beta函数的4圈系数β₃。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        float
            β₃系数值

        Notes
        -----
        包含Riemann zeta函数ζ(3)的贡献
        """
        val = (
            (149753 / 6 + 3564 * self.ZETA3)
            - (1078361 / 162 + 6508 / 27 * self.ZETA3) * nf
            + (50065 / 162 + 6472 / 81 * self.ZETA3) * nf**2
            + (1093 / 729) * nf**3
        )
        return val / (256.0 * self.PI**4)

    def _beta4(self, nf: int) -> float:
        """
        计算beta函数的5圈系数β₄。

        Parameters
        ----------
        nf : int
            活跃夸克数

        Returns
        -------
        float
            β₄系数值

        Notes
        -----
        这是目前已知的最高阶beta函数系数
        """
        return (
            537149.0 - 186163 * nf + 17571.8 * nf**2 - 231.281 * nf**3 - 1.84248 * nf**4
        ) / (1024.0 * self.PI**4)


def run_verification() -> None:
    """
    运行验证测试以确保AlphaSRunner的正确性。

    执行三个主要测试：
    1. 自洽性测试：验证在M_Z处计算的alpha_s与输入值一致
    2. 接口兼容性测试：验证kappa参数的正确性
    3. 已知值对比测试：与文献中的参考值进行对比

    Raises
    ------
    AssertionError
        当任何测试失败时抛出异常
    """
    print("\n" + "=" * 50)
    print("开始运行验证测试")
    print("=" * 50)

    # 创建AlphaSRunner实例（使用4圈精度）
    als_runner = AlphaSRunner(ALS_MZ=0.118, max_loop=4)

    # 测试1：自洽性检验
    print("\n[测试1: M_Z处的自洽性检验]")
    mz_val = als_runner.M_Z
    als_at_mz = als_runner.alpha_s(mz_val)
    print(f"  -> 输入值 alpha_s({mz_val:.2f} GeV)  = {als_runner.als_mz:.6f}")
    print(f"  -> 计算值 alpha_s({mz_val:.2f} GeV) = {als_at_mz:.6f}")
    assert np.isclose(als_runner.als_mz, als_at_mz, rtol=1e-7), "自洽性测试失败！"
    print("  ✓ 通过：在M_Z处计算值与输入值一致")

    # 测试2：kappa参数接口兼容性
    print("\n[测试2: kappa参数接口兼容性]")
    val_no_kappa = als_runner.alpha_s(100.0)
    val_with_kappa = als_runner.alpha_s(50.0, kappa=2.0)
    print(f"  -> alpha_s(100.0 GeV)           = {val_no_kappa:.6f}")
    print(f"  -> alpha_s(50.0 GeV, kappa=2.0) = {val_with_kappa:.6f}")
    assert np.isclose(val_no_kappa, val_with_kappa, rtol=1e-10), "kappa参数工作不正常！"
    print("  ✓ 通过：kappa参数正确实现能标缩放")

    # 测试3：与已知参考值对比
    print("\n[测试3: 与已知参考值对比]")
    test_scales = {
        10.0: 0.179,  # 10 GeV处的参考值
        172.5: 0.108,  # top夸克质量处的参考值
    }

    for scale, ref_val in test_scales.items():
        calc_val = als_runner.alpha_s(scale)
        relative_diff = abs(calc_val - ref_val) / ref_val * 100
        print(f"  - 能标 Q = {scale:.2f} GeV:")
        print(f"    计算值: {calc_val:.4f}")
        print(f"    参考值: {ref_val:.4f}")
        print(f"    相对差异: {relative_diff:.2f}%")
        assert np.isclose(calc_val, ref_val, rtol=0.03), f"在Q={scale} GeV处对比失败！"
    print("  ✓ 通过：计算值与参考值在误差范围内一致")

    # 额外测试：低能标行为
    print("\n[额外测试: 低能标行为]")
    low_scale_val = als_runner.alpha_s(1.0)
    print(f"  -> alpha_s(1.0 GeV) = {low_scale_val:.4f}")
    print(f"  -> 注：在低能标处alpha_s变大，接近非微扰区域")

    print("\n" + "=" * 50)
    print("所有验证测试通过！")
    print("=" * 50 + "\n")


if __name__ == "__main__":
    run_verification()
